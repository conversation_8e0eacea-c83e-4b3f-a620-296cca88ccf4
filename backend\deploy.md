# Backend Deployment Guide for Vercel

## Prerequisites
1. Install Vercel CLI: `npm i -g vercel`
2. Login to Vercel: `vercel login`

## Deployment Steps

### 1. Deploy to Vercel
```bash
cd backend
vercel --prod
```

### 2. Configure Environment Variables in Vercel Dashboard
After deployment, go to your Vercel dashboard and add these environment variables:

**Required API Keys:**
- `SERPAPI_KEY`: Your SerpAPI key for search functionality
- `GEMINI_API_KEY`: Your Google Gemini API key for AI analysis
- `OPENAI_API_KEY`: Your OpenAI API key for chatbot functionality

**Optional Configuration:**
- `MAX_SEARCH_RESULTS`: Maximum search results (default: 20)
- `DEFAULT_SEARCH_RESULTS`: Default search results (default: 5)
- `SEARCH_TIMEOUT`: Search timeout in seconds (default: 30)
- `NODE_ENV`: Set to "production"

### 3. Redeploy after setting environment variables
```bash
vercel --prod
```

### 4. Update Frontend API URL
After backend deployment, update your frontend's `REACT_APP_API_BASE_URL` environment variable in Vercel to point to your backend's Vercel URL.

## Important Notes

### API Endpoints
Your backend will be available at: `https://your-backend-name.vercel.app`

Main endpoints:
- Health check: `/health`
- API documentation: `/docs`
- QCM generation: `/api/generate-qcm`
- Summary generation: `/api/generate-summary`
- Search: `/api/search`
- Chatbot: `/api/chat`

### CORS Configuration
The backend is configured to accept requests from:
- `localhost:3000` (development)
- `*.vercel.app` (Vercel domains)
- All origins (for testing - should be restricted in production)

### Security
- All API keys are stored as environment variables in Vercel
- Never commit `.env` files to version control
- The `.env` file is only for local development

### Troubleshooting
1. **Import errors**: Make sure all dependencies are in `requirements.txt`
2. **Environment variables**: Check that all required env vars are set in Vercel dashboard
3. **CORS issues**: Verify that your frontend domain is allowed in CORS settings
4. **Cold starts**: Serverless functions may have cold start delays on first request
