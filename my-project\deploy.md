# Frontend Deployment Guide for Vercel

## Prerequisites
1. Install Vercel CLI: `npm i -g vercel`
2. Login to Vercel: `vercel login`

## Deployment Steps

### 1. Build the project locally (optional test)
```bash
npm run build
```

### 2. Deploy to Vercel
```bash
vercel --prod
```

### 3. Configure Environment Variables in Vercel Dashboard
After deployment, go to your Vercel dashboard and add these environment variables:

- `REACT_APP_FIREBASE_API_KEY`: Your Firebase API key
- `REACT_APP_FIREBASE_AUTH_DOMAIN`: Your Firebase auth domain
- `REACT_APP_FIREBASE_PROJECT_ID`: Your Firebase project ID
- `REACT_APP_FIREBASE_STORAGE_BUCKET`: Your Firebase storage bucket
- `REACT_APP_FIREBASE_MESSAGING_SENDER_ID`: Your Firebase messaging sender ID
- `REACT_APP_FIREBASE_APP_ID`: Your Firebase app ID
- `REACT_APP_FIREBASE_MEASUREMENT_ID`: Your Firebase measurement ID
- `REACT_APP_API_BASE_URL`: Your backend API URL (will be set after backend deployment)

### 4. Redeploy after setting environment variables
```bash
vercel --prod
```

## Notes
- The frontend will be accessible at your Vercel domain
- Make sure to update CORS settings in your backend to include your Vercel domain
- Environment variables are securely stored in Vercel and not exposed in the client bundle
