# 🚀 Carer-IA Deployment Guide for Vercel

This guide will help you deploy your Carer-IA application (React frontend + FastAPI backend) to Vercel securely.

## 📋 Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Vercel CLI**: Install globally
   ```bash
   npm install -g vercel
   ```
3. **Git Repository**: Your code should be in a Git repository (GitHub, GitLab, etc.)

## 🔐 Security Setup (COMPLETED)

✅ **API Keys Secured**: All hardcoded API keys have been moved to environment variables
✅ **Environment Files Created**: `.env` files created for both frontend and backend
✅ **Code Updated**: All services now use environment variables

## 🎯 Deployment Steps

### Step 1: Deploy Backend (FastAPI)

1. **Navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Login to Vercel**:
   ```bash
   vercel login
   ```

3. **Deploy backend**:
   ```bash
   vercel --prod
   ```

4. **Configure Environment Variables in Vercel Dashboard**:
   - Go to your Vercel dashboard
   - Select your backend project
   - Go to Settings → Environment Variables
   - Add these variables:
     - `SERPAPI_KEY`: Your SerpAPI key
     - `GEMINI_API_KEY`: Your Google Gemini API key  
     - `OPENAI_API_KEY`: Your OpenAI API key
     - `MAX_SEARCH_RESULTS`: 20
     - `DEFAULT_SEARCH_RESULTS`: 5
     - `SEARCH_TIMEOUT`: 30
     - `NODE_ENV`: production

5. **Redeploy to apply environment variables**:
   ```bash
   vercel --prod
   ```

6. **Note your backend URL**: Save the URL (e.g., `https://your-backend.vercel.app`)

### Step 2: Deploy Frontend (React)

1. **Navigate to frontend directory**:
   ```bash
   cd ../my-project
   ```

2. **Deploy frontend**:
   ```bash
   vercel --prod
   ```

3. **Configure Environment Variables in Vercel Dashboard**:
   - Go to your Vercel dashboard
   - Select your frontend project
   - Go to Settings → Environment Variables
   - Add these variables:
     - `REACT_APP_FIREBASE_API_KEY`: Your Firebase API key
     - `REACT_APP_FIREBASE_AUTH_DOMAIN`: Your Firebase auth domain
     - `REACT_APP_FIREBASE_PROJECT_ID`: Your Firebase project ID
     - `REACT_APP_FIREBASE_STORAGE_BUCKET`: Your Firebase storage bucket
     - `REACT_APP_FIREBASE_MESSAGING_SENDER_ID`: Your Firebase messaging sender ID
     - `REACT_APP_FIREBASE_APP_ID`: Your Firebase app ID
     - `REACT_APP_FIREBASE_MEASUREMENT_ID`: Your Firebase measurement ID
     - `REACT_APP_API_BASE_URL`: Your backend URL from Step 1

4. **Redeploy to apply environment variables**:
   ```bash
   vercel --prod
   ```

## 🔗 Connect Frontend to Backend

After both deployments:

1. **Update CORS settings** (if needed):
   - Your backend is already configured to accept requests from Vercel domains
   - If you have issues, check the CORS configuration in `backend/app/main.py`

2. **Test the connection**:
   - Visit your frontend URL
   - Try using features that call the backend (search, chatbot, etc.)

## 📱 Access Your Application

- **Frontend**: `https://your-frontend.vercel.app`
- **Backend API**: `https://your-backend.vercel.app`
- **API Documentation**: `https://your-backend.vercel.app/docs`

## 🛠️ Troubleshooting

### Common Issues:

1. **Environment Variables Not Working**:
   - Make sure you redeployed after setting environment variables
   - Check variable names match exactly (case-sensitive)

2. **CORS Errors**:
   - Verify your frontend domain is in the CORS allow list
   - Check browser console for specific error messages

3. **API Keys Invalid**:
   - Verify all API keys are correct and active
   - Check Vercel dashboard environment variables

4. **Build Failures**:
   - Check build logs in Vercel dashboard
   - Ensure all dependencies are in `requirements.txt` (backend) or `package.json` (frontend)

## 🔒 Security Best Practices

✅ **Environment Variables**: All sensitive data is stored in Vercel environment variables
✅ **No Hardcoded Keys**: All API keys removed from source code
✅ **Private Repository**: Keep your repository private
✅ **CORS Configuration**: Properly configured for production

## 📞 Support

If you encounter issues:
1. Check Vercel deployment logs
2. Review this guide
3. Check the individual deployment guides in `backend/deploy.md` and `my-project/deploy.md`

---

**🎉 Congratulations!** Your Carer-IA application is now securely deployed on Vercel!
